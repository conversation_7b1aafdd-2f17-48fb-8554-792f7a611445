#!/bin/bash

# Simple development script for E2E testing
# This script provides easy commands for local development

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
E2E_SCRIPT="$SCRIPT_DIR/e2e-services.sh"

# Make sure the main script is executable
chmod +x "$E2E_SCRIPT"

show_help() {
    echo -e "${BLUE}Sentry Ruby E2E Development Helper${NC}"
    echo ""
    echo "Quick commands for E2E testing:"
    echo ""
    echo -e "${GREEN}Development Commands:${NC}"
    echo "  ./scripts/dev-e2e.sh start     - Start services with Foreman (recommended)"
    echo "  ./scripts/dev-e2e.sh stop      - Stop all services"
    echo "  ./scripts/dev-e2e.sh restart   - Restart all services"
    echo "  ./scripts/dev-e2e.sh test      - Run E2E tests (services must be running)"
    echo "  ./scripts/dev-e2e.sh test-auto - Start services, run tests, stop services"
    echo ""
    echo -e "${GREEN}Monitoring Commands:${NC}"
    echo "  ./scripts/dev-e2e.sh status    - Check service status"
    echo "  ./scripts/dev-e2e.sh logs      - Show service logs"
    echo "  ./scripts/dev-e2e.sh clean     - Clean up all logs and temp files"
    echo ""
    echo -e "${GREEN}Advanced Commands:${NC}"
    echo "  ./scripts/dev-e2e.sh manual    - Start services manually (without Foreman)"
    echo "  ./scripts/dev-e2e.sh export    - Export systemd services"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  ./scripts/dev-e2e.sh start     # Start services for development"
    echo "  ./scripts/dev-e2e.sh test      # Run tests against running services"
    echo "  ./scripts/dev-e2e.sh test-auto # Full test cycle"
    echo ""
    echo -e "${YELLOW}URLs when running:${NC}"
    echo "  Rails API: http://localhost:5000"
    echo "  Svelte App: http://localhost:5001"
}

case "${1:-help}" in
    start)
        echo -e "${BLUE}Starting E2E services with Foreman...${NC}"
        "$E2E_SCRIPT" start-foreman
        ;;
    stop)
        echo -e "${BLUE}Stopping E2E services...${NC}"
        "$E2E_SCRIPT" stop
        ;;
    restart)
        echo -e "${BLUE}Restarting E2E services...${NC}"
        "$E2E_SCRIPT" restart-foreman
        ;;
    test)
        echo -e "${BLUE}Running E2E tests...${NC}"
        bundle exec rspec spec/features/ --format progress
        ;;
    test-auto)
        echo -e "${BLUE}Running E2E tests with automatic service management...${NC}"
        bundle exec rake spec:e2e_auto
        ;;
    status)
        "$E2E_SCRIPT" status
        ;;
    logs)
        "$E2E_SCRIPT" logs
        ;;
    clean)
        echo -e "${BLUE}Cleaning up E2E environment...${NC}"
        "$E2E_SCRIPT" clean
        ;;
    manual)
        echo -e "${BLUE}Starting E2E services manually...${NC}"
        "$E2E_SCRIPT" start
        ;;
    export)
        echo -e "${BLUE}Exporting systemd services...${NC}"
        "$E2E_SCRIPT" export-systemd
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo -e "${YELLOW}Unknown command: $1${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
