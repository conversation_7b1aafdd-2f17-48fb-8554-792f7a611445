#!/bin/bash

# E2E Services Management Script
# Manages Rails and Svelte mini applications for end-to-end testing

set -e

# Configuration
RAILS_PORT=5000
SVELTE_PORT=5001
RAILS_DIR="spec/apps/rails-mini"
SVELTE_DIR="spec/apps/svelte-mini"
RAILS_PID_FILE="/tmp/e2e-rails.pid"
SVELTE_PID_FILE="/tmp/e2e-svelte.pid"
LOG_DIR="tmp/e2e-logs"
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_INTERVAL=1

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create log directory
create_log_dir() {
    mkdir -p "$LOG_DIR"
}

# Check if a port is in use
is_port_in_use() {
    local port=$1
    if command -v lsof >/dev/null 2>&1; then
        lsof -i ":$port" >/dev/null 2>&1
    elif command -v netstat >/dev/null 2>&1; then
        netstat -tuln | grep ":$port " >/dev/null 2>&1
    else
        # Fallback: try to connect to the port
        timeout 1 bash -c "</dev/tcp/localhost/$port" >/dev/null 2>&1
    fi
}

# Wait for service to be ready
wait_for_service() {
    local port=$1
    local service_name=$2
    local timeout=$3
    local elapsed=0
    
    log_info "Waiting for $service_name to be ready on port $port..."
    
    while [ $elapsed -lt $timeout ]; do
        if is_port_in_use "$port"; then
            log_success "$service_name is ready on port $port"
            return 0
        fi
        sleep $HEALTH_CHECK_INTERVAL
        elapsed=$((elapsed + HEALTH_CHECK_INTERVAL))
    done
    
    log_error "$service_name failed to start within ${timeout}s"
    return 1
}

# Health check for Rails service
health_check_rails() {
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$RAILS_PORT/trace_headers" >/dev/null 2>&1; then
            log_success "Rails service health check passed"
            return 0
        fi
        log_info "Rails health check attempt $attempt/$max_attempts..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "Rails service health check failed"
    return 1
}

# Health check for Svelte service
health_check_svelte() {
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$SVELTE_PORT" >/dev/null 2>&1; then
            log_success "Svelte service health check passed"
            return 0
        fi
        log_info "Svelte health check attempt $attempt/$max_attempts..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "Svelte service health check failed"
    return 1
}

# Start Rails service
start_rails() {
    if [ -f "$RAILS_PID_FILE" ] && kill -0 "$(cat "$RAILS_PID_FILE")" 2>/dev/null; then
        log_warning "Rails service is already running (PID: $(cat "$RAILS_PID_FILE"))"
        return 0
    fi
    
    if is_port_in_use "$RAILS_PORT"; then
        log_error "Port $RAILS_PORT is already in use"
        return 1
    fi
    
    log_info "Starting Rails service..."
    create_log_dir
    
    cd "$RAILS_DIR"
    nohup ruby app.rb > "../../../$LOG_DIR/rails.log" 2>&1 &
    local pid=$!
    echo $pid > "../../../$RAILS_PID_FILE"
    cd - >/dev/null
    
    if wait_for_service "$RAILS_PORT" "Rails" "$HEALTH_CHECK_TIMEOUT"; then
        if health_check_rails; then
            log_success "Rails service started successfully (PID: $pid)"
            return 0
        else
            log_error "Rails service started but health check failed"
            stop_rails
            return 1
        fi
    else
        log_error "Rails service failed to start"
        stop_rails
        return 1
    fi
}

# Start Svelte service
start_svelte() {
    if [ -f "$SVELTE_PID_FILE" ] && kill -0 "$(cat "$SVELTE_PID_FILE")" 2>/dev/null; then
        log_warning "Svelte service is already running (PID: $(cat "$SVELTE_PID_FILE"))"
        return 0
    fi
    
    if is_port_in_use "$SVELTE_PORT"; then
        log_error "Port $SVELTE_PORT is already in use"
        return 1
    fi
    
    log_info "Starting Svelte service..."
    create_log_dir
    
    cd "$SVELTE_DIR"
    nohup npm run dev > "../../../$LOG_DIR/svelte.log" 2>&1 &
    local pid=$!
    echo $pid > "../../../$SVELTE_PID_FILE"
    cd - >/dev/null
    
    if wait_for_service "$SVELTE_PORT" "Svelte" "$HEALTH_CHECK_TIMEOUT"; then
        if health_check_svelte; then
            log_success "Svelte service started successfully (PID: $pid)"
            return 0
        else
            log_error "Svelte service started but health check failed"
            stop_svelte
            return 1
        fi
    else
        log_error "Svelte service failed to start"
        stop_svelte
        return 1
    fi
}

# Stop Rails service
stop_rails() {
    if [ -f "$RAILS_PID_FILE" ]; then
        local pid=$(cat "$RAILS_PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "Stopping Rails service (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "Rails service didn't stop gracefully, force killing..."
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f "$RAILS_PID_FILE"
        log_success "Rails service stopped"
    else
        log_info "Rails service is not running"
    fi
}

# Stop Svelte service
stop_svelte() {
    if [ -f "$SVELTE_PID_FILE" ]; then
        local pid=$(cat "$SVELTE_PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "Stopping Svelte service (PID: $pid)..."
            kill "$pid" 2>/dev/null || true
            sleep 2
            if kill -0 "$pid" 2>/dev/null; then
                log_warning "Svelte service didn't stop gracefully, force killing..."
                kill -9 "$pid" 2>/dev/null || true
            fi
        fi
        rm -f "$SVELTE_PID_FILE"
        log_success "Svelte service stopped"
    else
        log_info "Svelte service is not running"
    fi
}

# Start all services
start_all() {
    log_info "Starting all E2E services..."
    
    if start_rails && start_svelte; then
        log_success "All E2E services started successfully"
        return 0
    else
        log_error "Failed to start all services"
        stop_all
        return 1
    fi
}

# Stop all services
stop_all() {
    log_info "Stopping all E2E services..."
    stop_rails
    stop_svelte
    log_success "All E2E services stopped"
}

# Check status of services
status() {
    log_info "Checking E2E services status..."
    
    local rails_running=false
    local svelte_running=false
    
    if [ -f "$RAILS_PID_FILE" ] && kill -0 "$(cat "$RAILS_PID_FILE")" 2>/dev/null; then
        rails_running=true
        log_success "Rails service is running (PID: $(cat "$RAILS_PID_FILE"), Port: $RAILS_PORT)"
    else
        log_warning "Rails service is not running"
    fi
    
    if [ -f "$SVELTE_PID_FILE" ] && kill -0 "$(cat "$SVELTE_PID_FILE")" 2>/dev/null; then
        svelte_running=true
        log_success "Svelte service is running (PID: $(cat "$SVELTE_PID_FILE"), Port: $SVELTE_PORT)"
    else
        log_warning "Svelte service is not running"
    fi
    
    if $rails_running && $svelte_running; then
        log_success "All E2E services are running"
        return 0
    else
        log_warning "Some E2E services are not running"
        return 1
    fi
}

# Clean up logs and temporary files
clean() {
    log_info "Cleaning up E2E service files..."
    stop_all
    rm -rf "$LOG_DIR"
    rm -f "$RAILS_PID_FILE" "$SVELTE_PID_FILE"
    
    # Clean Rails logs
    if [ -f "$RAILS_DIR/log/sentry_debug_events.log" ]; then
        > "$RAILS_DIR/log/sentry_debug_events.log"
        log_info "Cleared Rails debug events log"
    fi
    
    log_success "Cleanup completed"
}

# Show logs
logs() {
    local service=${1:-"all"}
    
    case $service in
        rails)
            if [ -f "$LOG_DIR/rails.log" ]; then
                tail -f "$LOG_DIR/rails.log"
            else
                log_error "Rails log file not found"
            fi
            ;;
        svelte)
            if [ -f "$LOG_DIR/svelte.log" ]; then
                tail -f "$LOG_DIR/svelte.log"
            else
                log_error "Svelte log file not found"
            fi
            ;;
        all|*)
            log_info "Showing logs for all services (Ctrl+C to exit)..."
            if [ -f "$LOG_DIR/rails.log" ] && [ -f "$LOG_DIR/svelte.log" ]; then
                tail -f "$LOG_DIR/rails.log" "$LOG_DIR/svelte.log"
            else
                log_error "Log files not found. Make sure services are running."
            fi
            ;;
    esac
}

# Show usage
usage() {
    echo "E2E Services Management Script"
    echo ""
    echo "Usage: $0 {start|stop|restart|status|clean|logs|start-rails|start-svelte|stop-rails|stop-svelte}"
    echo ""
    echo "Commands:"
    echo "  start         Start all E2E services (Rails + Svelte)"
    echo "  stop          Stop all E2E services"
    echo "  restart       Restart all E2E services"
    echo "  status        Check status of all services"
    echo "  clean         Stop services and clean up logs/temp files"
    echo "  logs [rails|svelte|all]  Show logs (default: all)"
    echo "  start-rails   Start only Rails service"
    echo "  start-svelte  Start only Svelte service"
    echo "  stop-rails    Stop only Rails service"
    echo "  stop-svelte   Stop only Svelte service"
    echo ""
    echo "Examples:"
    echo "  $0 start                 # Start all services"
    echo "  $0 logs rails           # Show Rails logs"
    echo "  $0 status               # Check service status"
}

# Main command handling
case "${1:-}" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        stop_all
        sleep 2
        start_all
        ;;
    status)
        status
        ;;
    clean)
        clean
        ;;
    logs)
        logs "${2:-all}"
        ;;
    start-rails)
        start_rails
        ;;
    start-svelte)
        start_svelte
        ;;
    stop-rails)
        stop_rails
        ;;
    stop-svelte)
        stop_svelte
        ;;
    help|--help|-h)
        usage
        ;;
    *)
        log_error "Unknown command: ${1:-}"
        echo ""
        usage
        exit 1
        ;;
esac
