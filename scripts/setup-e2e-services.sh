#!/bin/bash

# Simple script to set up E2E services for local development
# Uses Foreman export to create systemd user services

set -e

USER_SYSTEMD_DIR="$HOME/.config/systemd/user"
EXPORT_DIR="/tmp/sentry-e2e-systemd"

echo "Setting up E2E services for local development..."

# Check if foreman is available
if ! command -v foreman >/dev/null 2>&1; then
    echo "Error: Foreman not found. Please install it with: gem install foreman"
    exit 1
fi

# Create export directory
mkdir -p "$EXPORT_DIR"

# Export systemd user services
echo "Exporting Foreman services to systemd..."
foreman export systemd "$EXPORT_DIR" \
    --app=sentry-e2e \
    --user="$(whoami)" \
    --log="$HOME/.local/share/sentry-e2e/logs"

# Create user systemd directory if it doesn't exist
mkdir -p "$USER_SYSTEMD_DIR"

# Copy service files to user systemd directory
echo "Installing systemd user services..."
cp "$EXPORT_DIR"/* "$USER_SYSTEMD_DIR/"

# Reload systemd user daemon
systemctl --user daemon-reload

# Enable the target
systemctl --user enable sentry-e2e.target

echo ""
echo "✅ E2E services installed successfully!"
echo ""
echo "Usage:"
echo "  systemctl --user start sentry-e2e.target    # Start all services"
echo "  systemctl --user stop sentry-e2e.target     # Stop all services"
echo "  systemctl --user status sentry-e2e.target   # Check status"
echo ""
echo "Individual services:"
echo "  systemctl --user start sentry-e2e-rails-1.service"
echo "  systemctl --user start sentry-e2e-svelte-1.service"
echo ""
echo "View logs:"
echo "  journalctl --user -u sentry-e2e.target -f"
echo "  journalctl --user -u sentry-e2e-rails-1.service -f"
echo "  journalctl --user -u sentry-e2e-svelte-1.service -f"
echo ""
echo "Services will be available at:"
echo "  Rails API: http://localhost:5000"
echo "  Svelte App: http://localhost:5001"

# Clean up export directory
rm -rf "$EXPORT_DIR"
