version: '3.8'

services:
  rails-mini:
    build:
      context: .
      dockerfile_inline: |
        FROM ruby:3.3-slim
        WORKDIR /app
        COPY spec/apps/rails-mini/app.rb .
        COPY sentry-ruby ./sentry-ruby
        COPY sentry-rails ./sentry-rails
        RUN apt-get update && apt-get install -y build-essential && rm -rf /var/lib/apt/lists/*
        EXPOSE 5000
        CMD ["ruby", "app.rb"]
    ports:
      - "5000:5000"
    environment:
      - RAILS_ENV=development
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/trace_headers"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 10s

  svelte-mini:
    build:
      context: spec/apps/svelte-mini
      dockerfile_inline: |
        FROM node:18-slim
        WORKDIR /app
        COPY package*.json ./
        RUN npm ci
        COPY . .
        EXPOSE 5001
        CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
    ports:
      - "5001:5001"
    depends_on:
      rails-mini:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 15s
