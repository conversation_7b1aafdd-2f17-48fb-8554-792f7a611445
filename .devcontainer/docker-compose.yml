services:
  sentry:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        IMAGE: ${IMAGE}
        TAG: ${TAG}
    volumes:
      - ..:/workspace/sentry:cached
    command: sleep infinity
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    env_file: [".env"]
    depends_on:
      - redis

  redis:
    image: redis:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"

  rails-mini:
    image: ruby:3.3-slim
    working_dir: /workspace/sentry/spec/apps/rails-mini
    volumes:
      - ..:/workspace/sentry:cached
    ports:
      - "5000:5000"
    command: ruby app.rb
    environment:
      - RAILS_ENV=development
    profiles:
      - e2e

  svelte-mini:
    image: node:18-slim
    working_dir: /workspace/sentry/spec/apps/svelte-mini
    volumes:
      - ..:/workspace/sentry:cached
    ports:
      - "5001:5001"
    command: npm run dev -- --host 0.0.0.0
    environment:
      - SENTRY_E2E_RAILS_APP_URL=http://rails-mini:5000
    profiles:
      - e2e
