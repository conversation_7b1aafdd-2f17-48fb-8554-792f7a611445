services:
  sentry:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        IMAGE: ${IMAGE}
        TAG: ${TAG}
    volumes:
      - ..:/workspace/sentry:cached
    command: sleep infinity
    environment:
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
    env_file: [".env"]
    depends_on:
      - redis

  redis:
    image: redis:latest
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "6379:6379"

  # E2E test services - simple approach
  rails-mini:
    image: ruby:3.3-slim
    working_dir: /workspace/sentry/spec/apps/rails-mini
    volumes:
      - ..:/workspace/sentry:cached
    ports:
      - "5000:5000"
    command: ruby app.rb
    environment:
      - RAILS_ENV=development
    profiles:
      - e2e

  svelte-mini:
    image: node:18-slim
    working_dir: /workspace/sentry/spec/apps/svelte-mini
    volumes:
      - ..:/workspace/sentry:cached
    ports:
      - "5001:5001"
    command: npm run dev -- --host 0.0.0.0
    profiles:
      - e2e

  # E2E test services
  rails-mini:
    build:
      context: ..
      dockerfile_inline: |
        FROM ruby:3.3-slim
        WORKDIR /app
        RUN apt-get update && apt-get install -y build-essential curl && rm -rf /var/lib/apt/lists/*
        COPY spec/apps/rails-mini/app.rb .
        COPY sentry-ruby ./sentry-ruby
        COPY sentry-rails ./sentry-rails
        EXPOSE 5000
        CMD ["ruby", "app.rb"]
    ports:
      - "5000:5000"
    environment:
      - RAILS_ENV=development
    profiles:
      - e2e

  svelte-mini:
    build:
      context: ../spec/apps/svelte-mini
      dockerfile_inline: |
        FROM node:18-slim
        WORKDIR /app
        RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
        COPY package*.json ./
        RUN npm ci
        COPY . .
        EXPOSE 5001
        CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
    ports:
      - "5001:5001"
    profiles:
      - e2e
