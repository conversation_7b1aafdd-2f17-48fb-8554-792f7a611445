# End-to-End (E2E) Testing Guide

This guide explains how to run and manage end-to-end tests for the Sentry Ruby SDK, which test distributed tracing between a Rails API and a Svelte frontend application.

## Overview

The E2E test setup consists of:

- **Rails Mini App** (`spec/apps/rails-mini/`): A minimal Rails API that triggers Sentry events
- **Svelte Mini App** (`spec/apps/svelte-mini/`): A frontend that makes requests to the Rails API
- **E2E Tests** (`spec/features/`): RSpec tests using Capybara to verify distributed tracing

## Quick Start

### Local Development (Recommended)

```bash
# Start services with <PERSON>eman (easiest for development)
./scripts/dev-e2e.sh start

# Run tests
./scripts/dev-e2e.sh test

# Stop services
./scripts/dev-e2e.sh stop
```

### One-Command Testing

```bash
# Start services, run tests, and stop services automatically
./scripts/dev-e2e.sh test-auto
```

## Service Management

### Using the Development Helper Script

The `scripts/dev-e2e.sh` script provides simple commands for common tasks:

```bash
./scripts/dev-e2e.sh start     # Start with Foreman (recommended)
./scripts/dev-e2e.sh stop      # Stop all services
./scripts/dev-e2e.sh restart   # Restart services
./scripts/dev-e2e.sh status    # Check service status
./scripts/dev-e2e.sh logs      # Show service logs
./scripts/dev-e2e.sh clean     # Clean up logs and temp files
```

### Using the Advanced Script

The `scripts/e2e-services.sh` script provides more control:

```bash
# Foreman-based management (recommended for local development)
./scripts/e2e-services.sh start-foreman
./scripts/e2e-services.sh stop-foreman
./scripts/e2e-services.sh restart-foreman

# Manual process management
./scripts/e2e-services.sh start
./scripts/e2e-services.sh stop
./scripts/e2e-services.sh restart

# Individual service control
./scripts/e2e-services.sh start-rails
./scripts/e2e-services.sh start-svelte
./scripts/e2e-services.sh stop-rails
./scripts/e2e-services.sh stop-svelte

# CI-optimized start
./scripts/e2e-services.sh start-ci

# Export systemd services for production
./scripts/e2e-services.sh export-systemd /path/to/export
```

### Using Foreman Directly

```bash
# Start all services defined in Procfile
foreman start

# Start specific service
foreman start rails
foreman start svelte

# Start with custom formation
foreman start -m rails=1,svelte=1
```

## Running Tests

### Using Rake Tasks

```bash
# Run E2E tests (requires services to be running)
bundle exec rake spec:e2e

# Run E2E tests with automatic service management
bundle exec rake spec:e2e_auto
```

### Using RSpec Directly

```bash
# Run all E2E tests
bundle exec rspec spec/features/

# Run specific test
bundle exec rspec spec/features/tracing_spec.rb
```

## Service URLs

When services are running:

- **Rails API**: http://localhost:5000
- **Svelte App**: http://localhost:5001

### Available Rails Endpoints

- `GET /error` - Triggers a ZeroDivisionError for testing
- `GET /trace_headers` - Returns current trace propagation headers
- `OPTIONS /*` - CORS preflight handler

## Troubleshooting

### Services Won't Start

1. **Check if ports are in use:**
   ```bash
   ./scripts/e2e-services.sh status
   lsof -i :5000
   lsof -i :5001
   ```

2. **Check logs:**
   ```bash
   ./scripts/e2e-services.sh logs
   ```

3. **Clean up and restart:**
   ```bash
   ./scripts/e2e-services.sh clean
   ./scripts/e2e-services.sh start
   ```

### Tests Failing

1. **Verify services are healthy:**
   ```bash
   curl http://localhost:5000/trace_headers
   curl http://localhost:5001
   ```

2. **Check Sentry debug logs:**
   ```bash
   cat spec/apps/rails-mini/log/sentry_debug_events.log
   ```

3. **Run tests with verbose output:**
   ```bash
   bundle exec rspec spec/features/ --format documentation
   ```

### Common Issues

- **Port conflicts**: Stop other services using ports 5000/5001
- **Node.js dependencies**: Run `npm install` in `spec/apps/svelte-mini/`
- **Ruby dependencies**: Run `bundle install` in the project root
- **Chrome/Chromium**: Ensure Chrome is installed for Selenium tests

## CI/CD Integration

### GitHub Actions

The E2E tests run automatically in GitHub Actions via `.github/workflows/e2e_tests.yml`:

- Triggered on pushes to master and PRs affecting E2E-related files
- Tests against Ruby 3.2 and 3.3
- Uses optimized CI service management
- Uploads logs and artifacts on failure

### Adding to Existing Workflows

To add E2E tests to other workflows:

```yaml
- name: Install Node.js
  uses: actions/setup-node@v4
  with:
    node-version: '18'
    cache: 'npm'
    cache-dependency-path: 'spec/apps/svelte-mini/package-lock.json'

- name: Install Chrome
  uses: browser-actions/setup-chrome@v1

- name: Install npm dependencies
  run: cd spec/apps/svelte-mini && npm ci

- name: Start E2E services
  run: ./scripts/e2e-services.sh start-ci

- name: Run E2E tests
  run: bundle exec rake spec:e2e

- name: Stop E2E services
  if: always()
  run: ./scripts/e2e-services.sh stop
```

## Production Deployment

### Using Systemd (Linux)

1. **Export systemd services:**
   ```bash
   ./scripts/e2e-services.sh export-systemd /tmp/sentry-e2e-systemd
   ```

2. **Install services:**
   ```bash
   sudo cp /tmp/sentry-e2e-systemd/* /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable sentry-e2e.target
   ```

3. **Start services:**
   ```bash
   sudo systemctl start sentry-e2e.target
   ```

### Using Docker Compose

Create a `docker-compose.e2e.yml`:

```yaml
version: '3.8'
services:
  rails-mini:
    build:
      context: .
      dockerfile: spec/apps/rails-mini/Dockerfile
    ports:
      - "5000:5000"
    
  svelte-mini:
    build:
      context: spec/apps/svelte-mini
    ports:
      - "5001:5001"
    depends_on:
      - rails-mini
```

## Development Tips

1. **Use Foreman for local development** - it's the most reliable and matches the Procfile
2. **Keep services running** during development to avoid startup delays
3. **Check service status** before running tests
4. **Use the development helper script** for common tasks
5. **Monitor logs** when debugging test failures

## File Structure

```
├── Procfile                           # Foreman process definitions
├── scripts/
│   ├── e2e-services.sh               # Advanced service management
│   └── dev-e2e.sh                    # Simple development helper
├── spec/
│   ├── apps/
│   │   ├── rails-mini/               # Rails API application
│   │   │   ├── app.rb               # Main Rails app file
│   │   │   └── log/                 # Sentry debug logs
│   │   └── svelte-mini/             # Svelte frontend application
│   │       ├── package.json         # Node.js dependencies
│   │       ├── vite.config.js       # Vite configuration
│   │       └── src/                 # Svelte source code
│   └── features/                     # E2E test files
│       └── tracing_spec.rb          # Main E2E test
├── .github/workflows/
│   └── e2e_tests.yml                # GitHub Actions workflow
└── docs/
    └── E2E_TESTING.md               # This documentation
```
