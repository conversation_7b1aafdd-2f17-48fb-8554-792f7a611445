# frozen_string_literal: true

require "rake/clean"
require_relative "lib/sentry/test/rake_tasks"

Sentry::Test::RakeTasks.define_spec_tasks()

# Define E2E test tasks
namespace :spec do
  desc "Run end-to-end tests (requires background services)"
  task :e2e do
    puts "Running E2E tests..."
    puts "Note: Make sure background services are running:"
    puts "  Local: systemctl --user start sentry-e2e.target"
    puts "  Docker: docker-compose -f docker-compose.e2e.yml up -d"
    puts "  Foreman: foreman start"

    system("bundle exec rspec spec/features/ --format progress") || exit(1)
  end

  desc "Run E2E tests with Docker Compose"
  task :e2e_docker do
    puts "Starting E2E services with Docker Compose and running tests..."

    begin
      # Start services
      unless system("docker-compose -f docker-compose.e2e.yml up -d")
        puts "Failed to start E2E services with Docker Compose"
        exit(1)
      end

      # Wait for services to be healthy
      puts "Waiting for services to be ready..."
      unless system("timeout 60 bash -c 'until docker-compose -f docker-compose.e2e.yml ps | grep -q \"healthy\"; do sleep 2; done'")
        puts "Services failed to become healthy"
        system("docker-compose -f docker-compose.e2e.yml down -v")
        exit(1)
      end

      # Run tests
      unless system("bundle exec rspec spec/features/ --format progress")
        puts "E2E tests failed"
        exit(1)
      end

    ensure
      # Always stop services
      system("docker-compose -f docker-compose.e2e.yml down -v")
    end
  end
end

task default: :spec
