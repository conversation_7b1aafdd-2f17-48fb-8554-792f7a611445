# frozen_string_literal: true

require "rake/clean"
require_relative "lib/sentry/test/rake_tasks"

Sentry::Test::RakeTasks.define_spec_tasks()

# Define E2E test tasks
namespace :spec do
  desc "Run end-to-end tests (requires background services)"
  task :e2e do
    puts "Running E2E tests..."
    puts "Note: Make sure background services are running with: ./scripts/e2e-services.sh start"

    system("bundle exec rspec spec/features/ --format progress") || exit(1)
  end

  desc "Run E2E tests with automatic service management"
  task :e2e_auto do
    puts "Starting E2E services and running tests..."

    begin
      # Start services
      unless system("./scripts/e2e-services.sh start")
        puts "Failed to start E2E services"
        exit(1)
      end

      # Run tests
      unless system("bundle exec rspec spec/features/ --format progress")
        puts "E2E tests failed"
        exit(1)
      end

    ensure
      # Always stop services
      system("./scripts/e2e-services.sh stop")
    end
  end
end

task default: :spec
