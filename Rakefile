# frozen_string_literal: true

require "rake/clean"
require_relative "lib/sentry/test/rake_tasks"

Sentry::Test::RakeTasks.define_spec_tasks()

# Define E2E test tasks
namespace :spec do
  desc "Run end-to-end tests (requires background services)"
  task :e2e do
    puts "Running E2E tests..."
    puts "Note: Make sure background services are running:"
    puts "  Foreman: foreman start"
    puts "  Docker: cd .devcontainer && docker-compose --profile e2e up -d"

    system("bundle exec rspec spec/features/ --format progress") || exit(1)
  end

  desc "Run E2E tests with Docker Compose (devcontainer setup)"
  task :e2e_docker do
    puts "Starting E2E services with devcontainer Docker Compose and running tests..."

    begin
      # Start services using devcontainer setup
      Dir.chdir('.devcontainer') do
        unless system("docker-compose --profile e2e up -d rails-mini svelte-mini")
          puts "Failed to start E2E services"
          exit(1)
        end
      end

      # Wait for services to be ready
      puts "Waiting for services to be ready..."
      unless system("timeout 60 bash -c 'until curl -s http://localhost:5000/trace_headers >/dev/null && curl -s http://localhost:5001 >/dev/null; do sleep 2; done'")
        puts "Services failed to become ready"
        Dir.chdir('.devcontainer') { system("docker-compose --profile e2e down") }
        exit(1)
      end

      # Run tests
      unless system("bundle exec rspec spec/features/ --format progress")
        puts "E2E tests failed"
        exit(1)
      end

    ensure
      # Always stop services
      Dir.chdir('.devcontainer') { system("docker-compose --profile e2e down") }
    end
  end
end

task default: :spec
