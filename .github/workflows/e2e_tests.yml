name: E2E Tests

on:
  workflow_dispatch:
  push:
    branches:
      - master
      - \d+-\d+
  pull_request:
    paths:
      - 'spec/features/**'
      - 'spec/apps/**'
      - 'Procfile'
      - 'scripts/e2e-services.sh'
      - '.github/workflows/e2e_tests.yml'

concurrency:
  group: e2e-tests-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15

    strategy:
      fail-fast: false
      matrix:
        ruby_version: ["3.2", "3.3"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby ${{ matrix.ruby_version }}
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ matrix.ruby_version }}
          bundler-cache: true

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'spec/apps/svelte-mini/package-lock.json'

      - name: Install Chrome for Selenium
        uses: browser-actions/setup-chrome@v1
        with:
          chrome-version: stable

      - name: Install npm dependencies for Svelte mini app
        run: |
          cd spec/apps/svelte-mini
          npm ci

      - name: Make e2e services script executable
        run: chmod +x scripts/e2e-services.sh

      - name: Start background services
        run: |
          # Use our e2e services script for CI
          ./scripts/e2e-services.sh start-ci

      - name: Run E2E tests
        run: |
          bundle exec rspec spec/features/ --format progress
        env:
          RAILS_ENV: test

      - name: Show service logs on failure
        if: failure()
        run: |
          echo "=== Service Status ==="
          ./scripts/e2e-services.sh status || true

          echo "=== Service Logs ==="
          ./scripts/e2e-services.sh logs || true

          echo "=== Sentry Debug Events ==="
          if [ -f spec/apps/rails-mini/log/sentry_debug_events.log ]; then
            cat spec/apps/rails-mini/log/sentry_debug_events.log
          else
            echo "Sentry debug events log not found"
          fi

      - name: Stop background services
        if: always()
        run: |
          # Use our script to stop services
          ./scripts/e2e-services.sh stop

      - name: Upload test artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-logs-ruby-${{ matrix.ruby_version }}
          path: |
            tmp/e2e-logs/*.log
            spec/apps/rails-mini/log/sentry_debug_events.log
          retention-days: 7
